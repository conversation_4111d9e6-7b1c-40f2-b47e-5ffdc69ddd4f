# AI Logo Generator

A powerful web application that generates professional logos using artificial intelligence. Built with Next.js, TypeScript, and OpenAI's DALL-E 3 API.

## Features

- **AI-Powered Logo Generation**: Create unique logos using OpenAI's DALL-E 3
- **Industry-Specific Designs**: Choose from various industries and styles
- **Color Customization**: Select from predefined color palettes or create custom ones
- **Real-time Customization**: Adjust colors, fonts, layouts, and more
- **Multiple Download Formats**: Export in PNG, JPG, SVG, and PDF formats
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Dark Mode Support**: Automatic dark/light theme switching

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **AI Integration**: OpenAI DALL-E 3 API
- **Icons**: Lucide React
- **Image Processing**: HTML5 Canvas API
- **File Downloads**: Browser APIs

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- OpenAI API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ai-logo-generator
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Add your OpenAI API key to `.env.local`:
```env
OPENAI_API_KEY=your_openai_api_key_here
```

5. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Enter Brand Information**: Fill in your company name, select industry, and choose a style
2. **Select Colors**: Pick from predefined color palettes or use custom colors
3. **Generate Logos**: The AI will create multiple logo variations
4. **Customize**: Adjust colors, fonts, layouts, and other design elements
5. **Download**: Export your final logo in your preferred format and size

## API Configuration

### OpenAI API Key

You'll need an OpenAI API key to use the logo generation feature:

1. Sign up at [OpenAI](https://platform.openai.com/)
2. Create an API key in your dashboard
3. Add it to your `.env.local` file

### Rate Limits

Be aware of OpenAI's rate limits and pricing:
- DALL-E 3: $0.040 per image (1024×1024)
- Consider implementing usage tracking for production

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add your environment variables in the Vercel dashboard
4. Deploy!

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Project Structure

```
src/
├── app/
│   ├── api/
│   │   ├── generate-logo/    # Logo generation endpoint
│   │   └── download/         # File download endpoint
│   ├── globals.css           # Global styles
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Main application
├── components/
│   ├── LogoForm.tsx         # Brand input form
│   ├── LogoGallery.tsx      # Generated logos display
│   ├── LogoCustomizer.tsx   # Customization interface
│   ├── DownloadOptions.tsx  # Export functionality
│   └── LoadingSpinner.tsx   # Loading states
└── types/
    └── logo.ts              # TypeScript interfaces
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

If you encounter any issues or have questions:
1. Check the [Issues](https://github.com/your-repo/issues) page
2. Create a new issue with detailed information
3. Include error messages and steps to reproduce

## Roadmap

- [ ] Advanced logo editing tools
- [ ] Brand kit generation
- [ ] Logo animation features
- [ ] Team collaboration
- [ ] Logo history and versioning
- [ ] Integration with design tools
