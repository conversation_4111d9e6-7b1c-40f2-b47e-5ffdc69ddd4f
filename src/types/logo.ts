export interface LogoFormData {
  companyName: string;
  industry: string;
  style: string;
  colors: string[];
  description?: string;
}

export interface Logo {
  id: string;
  imageUrl: string;
  prompt: string;
  companyName: string;
  industry: string;
  style: string;
  colors: string[];
  customizations?: LogoCustomizations;
}

export interface LogoCustomizations {
  backgroundColor?: string;
  textColor?: string;
  fontSize?: number;
  fontFamily?: string;
  layout?: 'horizontal' | 'vertical' | 'stacked';
  padding?: number;
  borderRadius?: number;
}

export interface DownloadFormat {
  format: 'png' | 'svg' | 'pdf' | 'jpg';
  size: {
    width: number;
    height: number;
  };
  quality?: number;
}

export const INDUSTRIES = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Retail',
  'Food & Beverage',
  'Real Estate',
  'Consulting',
  'Creative & Design',
  'Sports & Fitness',
  'Travel & Tourism',
  'Automotive',
  'Fashion',
  'Beauty & Wellness',
  'Non-profit',
  'Other'
] as const;

export const LOGO_STYLES = [
  'Modern & Minimalist',
  'Classic & Traditional',
  'Bold & Geometric',
  'Playful & Creative',
  'Elegant & Sophisticated',
  'Tech & Futuristic',
  'Vintage & Retro',
  'Hand-drawn & Organic'
] as const;

export const COLOR_PALETTES = [
  { name: 'Blue & White', colors: ['#2563eb', '#ffffff'] },
  { name: 'Black & Gold', colors: ['#000000', '#fbbf24'] },
  { name: 'Green & White', colors: ['#059669', '#ffffff'] },
  { name: 'Purple & Silver', colors: ['#7c3aed', '#e5e7eb'] },
  { name: 'Red & Black', colors: ['#dc2626', '#000000'] },
  { name: 'Orange & Blue', colors: ['#ea580c', '#2563eb'] },
  { name: 'Pink & Gray', colors: ['#ec4899', '#6b7280'] },
  { name: 'Teal & Cream', colors: ['#0d9488', '#fef7ed'] }
] as const;

export const FONT_FAMILIES = [
  'Inter',
  'Roboto',
  'Poppins',
  'Montserrat',
  'Open Sans',
  'Lato',
  'Source Sans Pro',
  'Nunito',
  'Raleway',
  'Ubuntu'
] as const;

export type Industry = typeof INDUSTRIES[number];
export type LogoStyle = typeof LOGO_STYLES[number];
export type FontFamily = typeof FONT_FAMILIES[number];
