import { NextRequest, NextResponse } from 'next/server';
import { Logo, DownloadFormat } from '@/types/logo';

export async function POST(request: NextRequest) {
  try {
    const { logo, format }: { logo: Logo; format: DownloadFormat } = await request.json();

    // Fetch the original image
    const imageResponse = await fetch(logo.imageUrl);
    if (!imageResponse.ok) {
      throw new Error('Failed to fetch logo image');
    }

    const imageBuffer = await imageResponse.arrayBuffer();
    
    // For now, we'll return the original image
    // In a production app, you'd want to process the image based on customizations
    // and convert to the requested format using a library like Sharp or Canvas
    
    let processedBuffer: ArrayBuffer;
    let contentType: string;
    
    switch (format.format) {
      case 'png':
        processedBuffer = imageBuffer;
        contentType = 'image/png';
        break;
      case 'jpg':
        processedBuffer = imageBuffer;
        contentType = 'image/jpeg';
        break;
      case 'svg':
        // For SVG, we'd need to convert the raster image to vector
        // This is a simplified implementation
        const svgContent = createSVGFromImage(logo, format);
        processedBuffer = new TextEncoder().encode(svgContent);
        contentType = 'image/svg+xml';
        break;
      case 'pdf':
        // For PDF, we'd use a library like jsPDF
        const pdfContent = await createPDFFromImage(imageBuffer, format);
        processedBuffer = pdfContent;
        contentType = 'application/pdf';
        break;
      default:
        throw new Error('Unsupported format');
    }

    const response = new NextResponse(processedBuffer);
    response.headers.set('Content-Type', contentType);
    response.headers.set(
      'Content-Disposition',
      `attachment; filename="${logo.companyName}-logo.${format.format}"`
    );

    return response;
  } catch (error) {
    console.error('Error processing download:', error);
    return NextResponse.json(
      { error: 'Failed to process download' },
      { status: 500 }
    );
  }
}

function createSVGFromImage(logo: Logo, format: DownloadFormat): string {
  const { width, height } = format.size;
  const customizations = logo.customizations;
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${customizations?.backgroundColor || '#ffffff'}"/>
  <image href="${logo.imageUrl}" x="10%" y="10%" width="80%" height="80%" preserveAspectRatio="xMidYMid meet"/>
  <text x="50%" y="90%" text-anchor="middle" font-family="${customizations?.fontFamily || 'Arial'}" font-size="${customizations?.fontSize || 24}" fill="${customizations?.textColor || '#000000'}">
    ${logo.companyName}
  </text>
</svg>`;
}

async function createPDFFromImage(imageBuffer: ArrayBuffer, format: DownloadFormat): Promise<ArrayBuffer> {
  // This is a simplified implementation
  // In a real app, you'd use a library like jsPDF or PDFKit
  
  // For now, we'll return a simple PDF structure
  // This is not a valid PDF - you'd need a proper PDF library
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj

xref
0 4
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
190
%%EOF`;

  return new TextEncoder().encode(pdfContent);
}
