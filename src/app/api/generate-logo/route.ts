import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { LogoFormData, Logo } from '@/types/logo';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const formData: LogoFormData = await request.json();

    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
      // Demo mode - return sample logos
      return NextResponse.json(generateDemoLogos(formData));
    }

    // Generate multiple logo variations
    const logoPromises = Array.from({ length: 4 }, (_, index) => 
      generateSingleLogo(formData, index)
    );

    const logos = await Promise.all(logoPromises);
    const validLogos = logos.filter(logo => logo !== null);

    if (validLogos.length === 0) {
      return NextResponse.json(
        { error: 'Failed to generate any logos' },
        { status: 500 }
      );
    }

    return NextResponse.json(validLogos);
  } catch (error) {
    console.error('Error generating logos:', error);
    return NextResponse.json(
      { error: 'Failed to generate logos' },
      { status: 500 }
    );
  }
}

async function generateSingleLogo(formData: LogoFormData, variation: number): Promise<Logo | null> {
  try {
    const prompt = createLogoPrompt(formData, variation);
    
    const response = await openai.images.generate({
      model: "dall-e-3",
      prompt,
      n: 1,
      size: "1024x1024",
      quality: "standard",
      style: "vivid"
    });

    const imageUrl = response.data[0]?.url;
    if (!imageUrl) {
      throw new Error('No image URL returned from OpenAI');
    }

    return {
      id: `logo-${Date.now()}-${variation}`,
      imageUrl,
      prompt,
      companyName: formData.companyName,
      industry: formData.industry,
      style: formData.style,
      colors: formData.colors
    };
  } catch (error) {
    console.error(`Error generating logo variation ${variation}:`, error);
    return null;
  }
}

function createLogoPrompt(formData: LogoFormData, variation: number): string {
  const { companyName, industry, style, colors, description } = formData;
  
  // Base prompt structure
  let prompt = `Create a professional logo for "${companyName}", a ${industry.toLowerCase()} company. `;
  
  // Add style guidance
  const stylePrompts = {
    'Modern & Minimalist': 'Design should be clean, simple, and contemporary with minimal elements',
    'Classic & Traditional': 'Design should be timeless, elegant, and sophisticated with traditional elements',
    'Bold & Geometric': 'Design should use strong geometric shapes and bold, striking elements',
    'Playful & Creative': 'Design should be fun, creative, and imaginative with playful elements',
    'Elegant & Sophisticated': 'Design should be refined, luxurious, and sophisticated',
    'Tech & Futuristic': 'Design should be modern, technological, and forward-thinking',
    'Vintage & Retro': 'Design should have a nostalgic, vintage feel with retro elements',
    'Hand-drawn & Organic': 'Design should feel natural, hand-crafted, and organic'
  };
  
  prompt += stylePrompts[style as keyof typeof stylePrompts] || 'Design should be professional and appropriate for the industry';
  
  // Add color guidance
  if (colors.length > 0) {
    const colorNames = colors.map(color => {
      // Convert hex to color names (simplified)
      const colorMap: { [key: string]: string } = {
        '#2563eb': 'blue',
        '#ffffff': 'white',
        '#000000': 'black',
        '#fbbf24': 'gold',
        '#059669': 'green',
        '#7c3aed': 'purple',
        '#e5e7eb': 'silver',
        '#dc2626': 'red',
        '#ea580c': 'orange',
        '#ec4899': 'pink',
        '#6b7280': 'gray',
        '#0d9488': 'teal',
        '#fef7ed': 'cream'
      };
      return colorMap[color] || color;
    });
    prompt += `. Use primarily ${colorNames.join(' and ')} colors`;
  }
  
  // Add variation-specific elements
  const variations = [
    '. Focus on typography and text-based design',
    '. Include an icon or symbol alongside the company name',
    '. Create an emblem or badge-style design',
    '. Design a monogram or lettermark style logo'
  ];
  
  prompt += variations[variation] || '';
  
  // Add description if provided
  if (description) {
    prompt += `. Additional context: ${description}`;
  }
  
  // Add technical requirements
  prompt += '. The logo should be clear, scalable, and work well on both light and dark backgrounds. Avoid complex details that would be lost when scaled down. The design should be professional and memorable.';
  
  return prompt;
}

function generateDemoLogos(formData: LogoFormData): Logo[] {
  // Sample logo URLs for demo purposes
  const demoImageUrls = [
    'https://via.placeholder.com/512x512/2563eb/ffffff?text=Logo+1',
    'https://via.placeholder.com/512x512/059669/ffffff?text=Logo+2',
    'https://via.placeholder.com/512x512/7c3aed/ffffff?text=Logo+3',
    'https://via.placeholder.com/512x512/dc2626/ffffff?text=Logo+4'
  ];

  return demoImageUrls.map((imageUrl, index) => ({
    id: `demo-logo-${Date.now()}-${index}`,
    imageUrl,
    prompt: `Demo logo for ${formData.companyName} in ${formData.style} style`,
    companyName: formData.companyName,
    industry: formData.industry,
    style: formData.style,
    colors: formData.colors
  }));
}
