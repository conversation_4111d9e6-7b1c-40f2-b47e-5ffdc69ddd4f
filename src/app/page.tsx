'use client';

import { useState, useEffect } from 'react';
import LogoForm from '@/components/LogoForm';
import LogoGallery from '@/components/LogoGallery';
import LogoCustomizer from '@/components/LogoCustomizer';
import DownloadOptions from '@/components/DownloadOptions';
import LoadingSpinner from '@/components/LoadingSpinner';
import { Logo, LogoFormData } from '@/types/logo';

export default function Home() {
  const [logos, setLogos] = useState<Logo[]>([]);
  const [selectedLogo, setSelectedLogo] = useState<Logo | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentStep, setCurrentStep] = useState<'form' | 'gallery' | 'customize' | 'download'>('form');
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleFormSubmit = async (formData: LogoFormData) => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/generate-logo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to generate logos');
      }

      const generatedLogos = await response.json();
      setLogos(generatedLogos);
      setCurrentStep('gallery');
    } catch (error) {
      console.error('Error generating logos:', error);
      alert('Failed to generate logos. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleLogoSelect = (logo: Logo) => {
    setSelectedLogo(logo);
    setCurrentStep('customize');
  };

  const handleLogoUpdate = (updatedLogo: Logo) => {
    setSelectedLogo(updatedLogo);
  };

  const handleBackToGallery = () => {
    setCurrentStep('gallery');
    setSelectedLogo(null);
  };

  const handleBackToForm = () => {
    setCurrentStep('form');
    setLogos([]);
    setSelectedLogo(null);
  };

  const handleProceedToDownload = () => {
    setCurrentStep('download');
  };

  if (!isClient) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
            AI Logo Generator
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Create stunning, professional logos for your brand using the power of artificial intelligence
          </p>
        </header>

        {/* Progress Indicator */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {['form', 'gallery', 'customize', 'download'].map((step, index) => (
              <div key={step} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep === step
                      ? 'bg-blue-600 text-white'
                      : index < ['form', 'gallery', 'customize', 'download'].indexOf(currentStep)
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-300 text-gray-600'
                  }`}
                >
                  {index + 1}
                </div>
                {index < 3 && (
                  <div
                    className={`w-12 h-1 mx-2 ${
                      index < ['form', 'gallery', 'customize', 'download'].indexOf(currentStep)
                        ? 'bg-green-500'
                        : 'bg-gray-300'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <main className="max-w-6xl mx-auto">
          {isGenerating && (
            <div className="flex justify-center items-center py-20">
              <LoadingSpinner />
            </div>
          )}

          {!isGenerating && currentStep === 'form' && (
            <LogoForm onSubmit={handleFormSubmit} />
          )}

          {!isGenerating && currentStep === 'gallery' && (
            <LogoGallery
              logos={logos}
              onLogoSelect={handleLogoSelect}
              onBackToForm={handleBackToForm}
            />
          )}

          {!isGenerating && currentStep === 'customize' && selectedLogo && (
            <LogoCustomizer
              logo={selectedLogo}
              onLogoUpdate={handleLogoUpdate}
              onBackToGallery={handleBackToGallery}
              onProceedToDownload={handleProceedToDownload}
            />
          )}

          {!isGenerating && currentStep === 'download' && selectedLogo && (
            <DownloadOptions
              logo={selectedLogo}
              onBackToCustomize={() => setCurrentStep('customize')}
              onStartOver={handleBackToForm}
            />
          )}
        </main>
      </div>
    </div>
  );
}
