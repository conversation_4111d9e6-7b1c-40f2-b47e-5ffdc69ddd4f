'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Logo, DownloadFormat } from '@/types/logo';
import { ArrowLeft, Download, FileImage, FileText, Repeat } from 'lucide-react';

interface DownloadOptionsProps {
  logo: Logo;
  onBackToCustomize: () => void;
  onStartOver: () => void;
}

const DOWNLOAD_FORMATS: DownloadFormat[] = [
  { format: 'png', size: { width: 512, height: 512 }, quality: 100 },
  { format: 'png', size: { width: 1024, height: 1024 }, quality: 100 },
  { format: 'png', size: { width: 2048, height: 2048 }, quality: 100 },
  { format: 'jpg', size: { width: 512, height: 512 }, quality: 90 },
  { format: 'jpg', size: { width: 1024, height: 1024 }, quality: 90 },
  { format: 'jpg', size: { width: 2048, height: 2048 }, quality: 90 },
  { format: 'svg', size: { width: 512, height: 512 } },
  { format: 'pdf', size: { width: 8.5, height: 11 } }, // inches for PDF
];

export default function DownloadOptions({ logo, onBackToCustomize, onStartOver }: DownloadOptionsProps) {
  const [isDownloading, setIsDownloading] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDownload = async (format: DownloadFormat) => {
    setIsDownloading(`${format.format}-${format.size.width}x${format.size.height}`);
    
    try {
      const response = await fetch('/api/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          logo,
          format
        }),
      });

      if (!response.ok) {
        throw new Error('Download failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${logo.companyName}-logo-${format.size.width}x${format.size.height}.${format.format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading logo:', error);
      alert('Failed to download logo. Please try again.');
    } finally {
      setIsDownloading(null);
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'svg':
        return <FileText className="w-5 h-5" />;
      case 'pdf':
        return <FileText className="w-5 h-5" />;
      default:
        return <FileImage className="w-5 h-5" />;
    }
  };

  const getFormatDescription = (format: DownloadFormat) => {
    switch (format.format) {
      case 'png':
        return 'High quality with transparency';
      case 'jpg':
        return 'Smaller file size, no transparency';
      case 'svg':
        return 'Vector format, infinitely scalable';
      case 'pdf':
        return 'Print-ready document format';
      default:
        return '';
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <button
          onClick={onBackToCustomize}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Customize</span>
        </button>
        
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Download Your Logo
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Choose your preferred format and size
          </p>
        </div>
        
        <button
          onClick={onStartOver}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <Repeat className="w-5 h-5" />
          <span>Start Over</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Logo Preview */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sticky top-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Final Design
            </h3>
            
            <div 
              className="aspect-square flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg mb-4"
              style={{
                backgroundColor: logo.customizations?.backgroundColor || '#ffffff',
                padding: `${logo.customizations?.padding || 20}px`,
                borderRadius: `${logo.customizations?.borderRadius || 0}px`
              }}
            >
              <div className="relative w-full h-full">
                <Image
                  src={logo.imageUrl}
                  alt={`${logo.companyName} logo`}
                  fill
                  className="object-contain"
                />
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold text-gray-900 dark:text-white">
                {logo.companyName}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {logo.industry} • {logo.style}
              </p>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Colors:</span>
                <div className="flex space-x-1">
                  {logo.colors.map((color, index) => (
                    <div
                      key={index}
                      className="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Download Options */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Download Formats
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {DOWNLOAD_FORMATS.map((format, index) => {
                const downloadKey = `${format.format}-${format.size.width}x${format.size.height}`;
                const isDownloading = isDownloading === downloadKey;
                
                return (
                  <button
                    key={index}
                    onClick={() => handleDownload(format)}
                    disabled={isDownloading}
                    className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getFormatIcon(format.format)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium text-gray-900 dark:text-white uppercase">
                            {format.format}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {format.format === 'pdf' 
                              ? `${format.size.width}" × ${format.size.height}"`
                              : `${format.size.width} × ${format.size.height}px`
                            }
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {getFormatDescription(format)}
                        </p>
                        
                        {format.quality && (
                          <p className="text-xs text-gray-500 dark:text-gray-500">
                            Quality: {format.quality}%
                          </p>
                        )}
                      </div>
                      
                      <div className="flex-shrink-0">
                        {isDownloading ? (
                          <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <Download className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>

            {/* Usage Tips */}
            <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2">
                Usage Recommendations
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
                <li>• <strong>PNG:</strong> Best for web use, social media, and when you need transparency</li>
                <li>• <strong>JPG:</strong> Good for email signatures and when file size matters</li>
                <li>• <strong>SVG:</strong> Perfect for websites and when you need to scale to any size</li>
                <li>• <strong>PDF:</strong> Ideal for print materials and professional documents</li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="mt-6 flex space-x-4">
              <button
                onClick={onBackToCustomize}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
              >
                Back to Customize
              </button>
              <button
                onClick={onStartOver}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
              >
                Create Another Logo
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
