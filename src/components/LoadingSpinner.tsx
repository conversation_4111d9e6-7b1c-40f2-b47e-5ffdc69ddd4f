'use client';

export default function LoadingSpinner() {
  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className="relative">
        <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-400 rounded-full animate-spin animation-delay-150"></div>
      </div>
      
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Creating Your Logos
        </h3>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Our AI is crafting unique logo designs based on your preferences. This may take a few moments...
        </p>
      </div>
      
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce animation-delay-100"></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce animation-delay-200"></div>
      </div>
    </div>
  );
}
