'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Logo } from '@/types/logo';
import { ArrowLeft, Download, Edit3 } from 'lucide-react';

interface LogoGalleryProps {
  logos: Logo[];
  onLogoSelect: (logo: Logo) => void;
  onBackToForm: () => void;
}

export default function LogoGallery({ logos, onLogoSelect, onBackToForm }: LogoGalleryProps) {
  const [selectedLogoId, setSelectedLogoId] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleLogoClick = (logo: Logo) => {
    setSelectedLogoId(logo.id);
  };

  const handleCustomizeClick = (logo: Logo) => {
    onLogoSelect(logo);
  };

  const handleQuickDownload = async (logo: Logo) => {
    try {
      const response = await fetch(logo.imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${logo.companyName}-logo.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading logo:', error);
      alert('Failed to download logo. Please try again.');
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <button
          onClick={onBackToForm}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Form</span>
        </button>
        
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Your Generated Logos
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Choose a logo to customize or download
          </p>
        </div>
        
        <div className="w-24"></div> {/* Spacer for centering */}
      </div>

      {/* Logo Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {logos.map((logo) => (
          <div
            key={logo.id}
            className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-200 cursor-pointer ${
              selectedLogoId === logo.id
                ? 'ring-4 ring-blue-500 transform scale-105'
                : 'hover:shadow-xl hover:transform hover:scale-102'
            }`}
            onClick={() => handleLogoClick(logo)}
          >
            {/* Logo Image */}
            <div className="aspect-square bg-gray-50 dark:bg-gray-700 flex items-center justify-center p-8">
              <div className="relative w-full h-full">
                <Image
                  src={logo.imageUrl}
                  alt={`${logo.companyName} logo`}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            </div>

            {/* Logo Info */}
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {logo.companyName}
              </h3>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
                  {logo.industry}
                </span>
                <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs rounded-full">
                  {logo.style}
                </span>
              </div>

              {/* Color Palette */}
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm text-gray-600 dark:text-gray-400">Colors:</span>
                <div className="flex space-x-1">
                  {logo.colors.map((color, index) => (
                    <div
                      key={index}
                      className="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCustomizeClick(logo);
                  }}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
                >
                  <Edit3 className="w-4 h-4" />
                  <span>Customize</span>
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleQuickDownload(logo);
                  }}
                  className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                  <Download className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {logos.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 dark:text-gray-600 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
            No logos generated yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Fill out the form to generate your first set of logos
          </p>
        </div>
      )}
    </div>
  );
}
