'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { Logo, LogoCustomizations, FONT_FAMILIES } from '@/types/logo';
import { ArrowLeft, Download } from 'lucide-react';

interface LogoCustomizerProps {
  logo: Logo;
  onLogoUpdate: (updatedLogo: Logo) => void;
  onBackToGallery: () => void;
  onProceedToDownload: () => void;
}

export default function LogoCustomizer({
  logo,
  onLogoUpdate,
  onBackToGallery,
  onProceedToDownload
}: LogoCustomizerProps) {
  const [customizations, setCustomizations] = useState<LogoCustomizations>(
    logo.customizations || {
      backgroundColor: '#ffffff',
      textColor: '#000000',
      fontSize: 24,
      fontFamily: 'Inter',
      layout: 'horizontal',
      padding: 20,
      borderRadius: 0
    }
  );
  const [isClient, setIsClient] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (isClient) {
      const updatedLogo = { ...logo, customizations };
      onLogoUpdate(updatedLogo);
    }
  }, [customizations, logo, onLogoUpdate, isClient]);

  const handleCustomizationChange = (key: keyof LogoCustomizations, value: any) => {
    setCustomizations(prev => ({ ...prev, [key]: value }));
  };

  const ColorPicker = ({ label, value, onChange }: { label: string; value: string; onChange: (color: string) => void }) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      <div className="flex items-center space-x-3">
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          placeholder="#000000"
        />
      </div>
    </div>
  );

  if (!isClient) {
    return (
      <div className="max-w-6xl mx-auto flex items-center justify-center py-20">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading customizer...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <button
          onClick={onBackToGallery}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Gallery</span>
        </button>
        
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Customize Your Logo
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Adjust colors, fonts, and layout to perfect your design
          </p>
        </div>
        
        <button
          onClick={onProceedToDownload}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Download</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Preview Panel */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Preview
          </h3>
          
          <div 
            className="aspect-square flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg"
            style={{
              backgroundColor: customizations.backgroundColor,
              padding: `${customizations.padding}px`,
              borderRadius: `${customizations.borderRadius}px`
            }}
          >
            <div className="relative w-full h-full max-w-sm max-h-sm">
              <Image
                src={logo.imageUrl}
                alt={`${logo.companyName} logo`}
                fill
                className="object-contain"
                style={{
                  filter: customizations.textColor !== '#000000' ? `hue-rotate(${customizations.textColor})` : 'none'
                }}
              />
            </div>
          </div>

          {/* Logo Info */}
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="font-semibold text-gray-900 dark:text-white">
              {logo.companyName}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {logo.industry} • {logo.style}
            </p>
          </div>
        </div>

        {/* Customization Panel */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            Customization Options
          </h3>

          <div className="space-y-6">
            {/* Background Color */}
            <ColorPicker
              label="Background Color"
              value={customizations.backgroundColor || '#ffffff'}
              onChange={(color) => handleCustomizationChange('backgroundColor', color)}
            />

            {/* Text Color */}
            <ColorPicker
              label="Text/Accent Color"
              value={customizations.textColor || '#000000'}
              onChange={(color) => handleCustomizationChange('textColor', color)}
            />

            {/* Font Family */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Font Family
              </label>
              <select
                value={customizations.fontFamily || 'Inter'}
                onChange={(e) => handleCustomizationChange('fontFamily', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                {FONT_FAMILIES.map((font) => (
                  <option key={font} value={font}>
                    {font}
                  </option>
                ))}
              </select>
            </div>

            {/* Font Size */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Font Size: {customizations.fontSize || 24}px
              </label>
              <input
                type="range"
                min="12"
                max="48"
                value={customizations.fontSize || 24}
                onChange={(e) => handleCustomizationChange('fontSize', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>

            {/* Layout */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Layout
              </label>
              <div className="grid grid-cols-3 gap-2">
                {['horizontal', 'vertical', 'stacked'].map((layout) => (
                  <button
                    key={layout}
                    onClick={() => handleCustomizationChange('layout', layout)}
                    className={`py-2 px-3 text-sm rounded-lg border transition-colors ${
                      customizations.layout === layout
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    {layout.charAt(0).toUpperCase() + layout.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Padding */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Padding: {customizations.padding || 20}px
              </label>
              <input
                type="range"
                min="0"
                max="50"
                value={customizations.padding || 20}
                onChange={(e) => handleCustomizationChange('padding', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>

            {/* Border Radius */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Border Radius: {customizations.borderRadius || 0}px
              </label>
              <input
                type="range"
                min="0"
                max="50"
                value={customizations.borderRadius || 0}
                onChange={(e) => handleCustomizationChange('borderRadius', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex space-x-4">
            <button
              onClick={onBackToGallery}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Back to Gallery
            </button>
            <button
              onClick={onProceedToDownload}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Proceed to Download
            </button>
          </div>
        </div>
      </div>

      {/* Hidden canvas for rendering */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
}
